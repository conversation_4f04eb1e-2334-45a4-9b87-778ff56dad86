import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torchvision import transforms
from torchvision.datasets import MNIST
import os
import matplotlib.pyplot as plt

# 专门为手写BP算法创建的numpy CNN类
class CNN_BP:
    def __init__(self):
        # CNN结构：1个卷积层+池化层+全连接层
        self.input_channels = 1
        self.output_channels = 16
        self.kernel_size = 3
        self.input_size = 28
        self.output_size = 10

        # 计算卷积后的尺寸
        self.conv_output_size = self.input_size - self.kernel_size + 1  # 28-3+1=26
        self.pool_output_size = self.conv_output_size // 2  # 26/2=13

        # 初始化权重和偏置 - 使用更好的初始化
        # 卷积核权重 (output_channels, input_channels, kernel_size, kernel_size)
        self.W_conv = np.random.randn(self.output_channels, self.input_channels,
                                     self.kernel_size, self.kernel_size) * np.sqrt(2.0 / (self.input_channels * self.kernel_size * self.kernel_size))
        # 卷积偏置 (output_channels)
        self.b_conv = np.zeros(self.output_channels)

        # 全连接层权重
        input_fc = self.output_channels * self.pool_output_size * self.pool_output_size
        self.W_fc = np.random.randn(input_fc, self.output_size) * np.sqrt(2.0 / input_fc)
        self.b_fc = np.zeros(self.output_size)

    def relu(self, x):
        return np.maximum(0, x)

    def softmax(self, x):
        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=1, keepdims=True)

    def convolution2d(self, input_data, filter_weights, bias):
        # 获取输入尺寸
        batch_size, in_channels, height, width = input_data.shape
        out_channels, _, filter_height, filter_width = filter_weights.shape

        # 计算输出尺寸
        out_height = height - filter_height + 1
        out_width = width - filter_width + 1

        # 初始化输出
        output = np.zeros((batch_size, out_channels, out_height, out_width))

        # 执行卷积操作
        for b in range(batch_size):
            for oc in range(out_channels):
                for ic in range(in_channels):
                    for i in range(out_height):
                        for j in range(out_width):
                            region = input_data[b, ic, i:i+filter_height, j:j+filter_width]
                            output[b, oc, i, j] += np.sum(region * filter_weights[oc, ic])
                output[b, oc] += bias[oc]

        return output

    def max_pooling2d(self, input_data, pool_size=2):
        # 获取输入尺寸
        batch_size, channels, height, width = input_data.shape

        # 计算输出尺寸
        out_height = height // pool_size
        out_width = width // pool_size

        # 初始化输出
        output = np.zeros((batch_size, channels, out_height, out_width))

        # 执行最大池化
        for b in range(batch_size):
            for c in range(channels):
                for i in range(out_height):
                    for j in range(out_width):
                        output[b, c, i, j] = np.max(input_data[b, c,
                                                   i*pool_size:(i+1)*pool_size,
                                                   j*pool_size:(j+1)*pool_size])

        return output

# 数据预处理
transform = transforms.Compose([transforms.ToTensor()])

# 加载数据集
train_dataset = MNIST(root=".", train=True, transform=transform, download=True)
test_dataset = MNIST(root=".", train=False, transform=transform, download=True)

# 使用更小的batch_size和数据量，因为手写CNN的计算极为复杂
batch_size = 8  # 减小batch size
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=batch_size)

# 实例化模型
model = CNN_BP()

# 超参数
learning_rate = 0.005
epochs = 10
loss_history = []

# 辅助函数
def pad2d(input_array, pad_width):
    """对输入进行padding"""
    return np.pad(input_array, pad_width, mode='constant')

def dilate2d(input_array, dilation):
    """对输入进行膨胀操作"""
    if dilation == 1:
        return input_array

    h, w = input_array.shape
    new_h, new_w = h + (h-1)*(dilation-1), w + (w-1)*(dilation-1)
    dilated = np.zeros((new_h, new_w))

    for i in range(h):
        for j in range(w):
            dilated[i*dilation, j*dilation] = input_array[i, j]

    return dilated

def rotate180(input_array):
    """将卷积核旋转180度"""
    return np.rot90(np.rot90(input_array))

# 手动实现BP算法训练函数
def train_model_with_bp(model, train_loader, epochs=1):
    """手动实现BP算法训练模型"""
    for epoch in range(epochs):
        total_loss = 0
        batch_count = 0

        for images, labels in train_loader:
            batch_count += 1
            # 为了演示，限制每个epoch训练50个batch（可以调整）
            if batch_count > 50:
                break
            # 将PyTorch张量转换为NumPy数组
            x_batch = images.numpy()  # [batch, 1, 28, 28]

            # 创建one-hot编码的标签
            y_batch = np.zeros((labels.shape[0], 10))
            for i, label in enumerate(labels.numpy()):
                y_batch[i, label] = 1

            # 前向传播
            # 卷积层
            conv_output = model.convolution2d(x_batch, model.W_conv, model.b_conv)
            relu_output = model.relu(conv_output)

            # 池化层
            pool_output = model.max_pooling2d(relu_output)

            # 展平
            flatten_output = pool_output.reshape(x_batch.shape[0], -1)

            # 全连接层
            fc_output = np.dot(flatten_output, model.W_fc) + model.b_fc
            output = model.softmax(fc_output)

            # 计算损失 (交叉熵损失)
            batch_loss = -np.sum(y_batch * np.log(output + 1e-10)) / x_batch.shape[0]
            total_loss += batch_loss

            # 反向传播
            # 输出层梯度
            dout = output - y_batch  # softmax的梯度与交叉熵结合 [batch, 10]

            # 全连接层梯度
            dW_fc = np.dot(flatten_output.T, dout) / x_batch.shape[0]  # [flatten_size, 10]
            db_fc = np.sum(dout, axis=0) / x_batch.shape[0]  # [10]

            # 反向传播到flatten层
            dflatten = np.dot(dout, model.W_fc.T)  # [batch, flatten_size]

            # 重塑回池化层输出形状
            batch_size = x_batch.shape[0]
            dpool = dflatten.reshape(batch_size, model.output_channels, model.pool_output_size, model.pool_output_size)

            # 池化层反向传播 (最大池化的梯度)
            # 创建与卷积层输出相同大小的梯度数组，并填充池化梯度
            dconv_output = np.zeros_like(relu_output)

            # 池化反向传播 - 这是最复杂的部分
            for b in range(batch_size):
                for c in range(model.output_channels):
                    for i in range(model.pool_output_size):
                        for j in range(model.pool_output_size):
                            # 找出该池化区域的最大值位置
                            pool_region = relu_output[b, c, i*2:(i+1)*2, j*2:(j+1)*2]
                            max_i, max_j = np.unravel_index(np.argmax(pool_region), pool_region.shape)

                            # 梯度只流向最大值位置
                            dconv_output[b, c, i*2 + max_i, j*2 + max_j] = dpool[b, c, i, j]

            # ReLU层反向传播
            drelu = dconv_output * (conv_output > 0)

            # 卷积层反向传播
            # 初始化梯度
            dW_conv = np.zeros_like(model.W_conv)
            db_conv = np.zeros_like(model.b_conv)

            # 计算卷积核梯度
            for b in range(batch_size):
                for oc in range(model.output_channels):
                    for ic in range(model.input_channels):
                        for i in range(model.conv_output_size):
                            for j in range(model.conv_output_size):
                                # 获取输入区域
                                input_region = x_batch[b, ic, i:i+model.kernel_size, j:j+model.kernel_size]
                                # 梯度累加
                                dW_conv[oc, ic] += input_region * drelu[b, oc, i, j]

                    # 偏置梯度
                    db_conv[oc] += np.sum(drelu[b, oc])

            # 正则化梯度
            dW_conv /= batch_size
            db_conv /= batch_size

            # 更新权重和偏置
            model.W_fc -= learning_rate * dW_fc
            model.b_fc -= learning_rate * db_fc
            model.W_conv -= learning_rate * dW_conv
            model.b_conv -= learning_rate * db_conv


        # 记录每个epoch的总损失
        epoch_loss = total_loss / batch_count  # 使用实际训练的batch数量
        loss_history.append(epoch_loss)
        print(f"Epoch {epoch+1} 完成，平均损失: {epoch_loss:.4f}，训练了 {batch_count} 个batch")

    # 绘制损失曲线
    plt.figure(figsize=(10, 5))
    plt.plot(range(1, epochs + 1), loss_history)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('CNN训练损失曲线')
    plt.savefig('cnn_bp_loss.png')


# 测试函数
def test_model(model, test_loader):
    correct = 0
    total = 0
    test_batch_count = 0

    for images, labels in test_loader:
        test_batch_count += 1
        # 限制测试batch数量，避免测试时间过长
        if test_batch_count > 20:
            break
        # 将PyTorch张量转换为NumPy数组并进行前向传播
        x_test = images.numpy()

        # 前向传播
        conv_output = model.convolution2d(x_test, model.W_conv, model.b_conv)
        relu_output = model.relu(conv_output)
        pool_output = model.max_pooling2d(relu_output)
        flatten_output = pool_output.reshape(x_test.shape[0], -1)
        fc_output = np.dot(flatten_output, model.W_fc) + model.b_fc
        predictions = model.softmax(fc_output)

        # 获取预测的类别
        predicted_classes = np.argmax(predictions, axis=1)
        labels_np = labels.numpy()

        # 计算准确率
        correct += np.sum(predicted_classes == labels_np)
        total += labels.shape[0]

    accuracy = 100 * correct / total
    print(f"测试准确率: {accuracy:.2f}%")
    return accuracy

# 将NumPy模型转换为PyTorch格式
def convert_to_pytorch_state_dict(model):
    """将NumPy模型权重转换为PyTorch state_dict格式"""
    import torch
    import torch.nn as nn

    # 创建一个简单的CNN模型作为参考结构
    class PyTorchCNN(nn.Module):
        def __init__(self):
            super(PyTorchCNN, self).__init__()
            self.conv1 = nn.Conv2d(1, 16, kernel_size=3)
            self.fc1 = nn.Linear(16 * 13 * 13, 10)

        def forward(self, x):
            x = F.relu(self.conv1(x))
            x = F.max_pool2d(x, 2)
            x = x.view(-1, 16 * 13 * 13)
            x = self.fc1(x)
            return x

    # 创建一个空的PyTorch模型
    pytorch_model = PyTorchCNN()

    # 复制权重
    state_dict = pytorch_model.state_dict()

    # 复制NumPy权重到PyTorch state_dict
    with torch.no_grad():
        state_dict['conv1.weight'] = torch.FloatTensor(model.W_conv)
        state_dict['conv1.bias'] = torch.FloatTensor(model.b_conv)
        state_dict['fc1.weight'] = torch.FloatTensor(model.W_fc.T)
        state_dict['fc1.bias'] = torch.FloatTensor(model.b_fc)

    return state_dict

# 开始训练
train_model_with_bp(model, train_loader, epochs=epochs)

# 测试模型
test_model(model, test_loader)

# 保存模型
os.makedirs("models", exist_ok=True)
state_dict = convert_to_pytorch_state_dict(model)
torch.save(state_dict, "models/cnn_bp.pth")
print("使用手写BP算法训练的CNN模型已成功保存到 models/cnn_bp.pth")