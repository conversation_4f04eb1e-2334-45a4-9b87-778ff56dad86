import torch
import torch.nn as nn
import torch.nn.functional as F

class CNN(nn.Module):
    def __init__(self):
        super(CNN, self).__init__()
        # 与训练文件中相同的网络结构
        self.conv1 = nn.Conv2d(1, 16, kernel_size=3)
        self.conv2 = nn.Conv2d(16, 32, kernel_size=3)

        self.pool = nn.MaxPool2d(2, 2)

        self.dropout1 = nn.Dropout(0.25)
        self.dropout2 = nn.Dropout(0.5)

        self.fc1 = nn.Linear(32 * 5 * 5, 128)
        self.fc2 = nn.Linear(128, 10)

        # 兼容旧版本的numpy实现
        self.is_pytorch_model = True

    def forward(self, x):
        # 第一个卷积块
        x = self.pool(F.relu(self.conv1(x)))
        x = self.dropout1(x)

        # 第二个卷积块
        x = self.pool(F.relu(self.conv2(x)))
        x = self.dropout1(x)

        # 展平
        x = x.view(-1, 32 * 5 * 5)

        # 全连接层
        x = F.relu(self.fc1(x))
        x = self.dropout2(x)
        x = self.fc2(x)

        return x