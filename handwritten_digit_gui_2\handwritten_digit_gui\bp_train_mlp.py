import numpy as np
import torch
from torch.utils.data import DataLoader
from torchvision import transforms
from torchvision.datasets import MNIST
# 创建与新PyTorch模型兼容的numpy MLP类
class MLP_BP:
    def __init__(self):
        # 4层网络结构
        self.input_size = 784
        self.hidden1_size = 256
        self.hidden2_size = 128
        self.hidden3_size = 64
        self.output_size = 10

        # 用Xavier初始化
        self.W1 = np.random.randn(self.input_size, self.hidden1_size) * np.sqrt(2.0 / self.input_size)
        self.b1 = np.zeros((1, self.hidden1_size))
        self.W2 = np.random.randn(self.hidden1_size, self.hidden2_size) * np.sqrt(2.0 / self.hidden1_size)
        self.b2 = np.zeros((1, self.hidden2_size))
        self.W3 = np.random.randn(self.hidden2_size, self.hidden3_size) * np.sqrt(2.0 / self.hidden2_size)
        self.b3 = np.zeros((1, self.hidden3_size))
        self.W4 = np.random.randn(self.hidden3_size, self.output_size) * np.sqrt(2.0 / self.hidden3_size)
        self.b4 = np.zeros((1, self.output_size))

    def relu(self, x):
        return np.maximum(0, x)

    def softmax(self, x):
        exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=1, keepdims=True)
import os
import matplotlib.pyplot as plt

# 数据预处理
transform = transforms.Compose([transforms.ToTensor()])

# 加载数据集
train_dataset = MNIST(root=".", train=True, transform=transform, download=True)
test_dataset = MNIST(root=".", train=False, transform=transform, download=True)

train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=64)

# 实例化模型
model = MLP_BP()

# 超参数
learning_rate = 0.01
epochs = 5
batch_size = 64
loss_history = []

# 手写BP算法训练函数
def train_model_with_bp(model, train_loader, epochs=5):

    for epoch in range(epochs):
        total_loss = 0
        batch_count = 0

        for images, labels in train_loader:
            batch_count += 1
            # 将PyTorch张量转换为NumPy数组
            x_batch = images.numpy().reshape(-1, 784)  # 展平图像

            # 创建one-hot编码的标签
            y_batch = np.zeros((labels.shape[0], 10))
            for i, label in enumerate(labels.numpy()):
                y_batch[i, label] = 1

            # 前向传播 - 4层网络
            z1 = np.dot(x_batch, model.W1) + model.b1
            a1 = model.relu(z1)

            z2 = np.dot(a1, model.W2) + model.b2
            a2 = model.relu(z2)

            z3 = np.dot(a2, model.W3) + model.b3
            a3 = model.relu(z3)

            z4 = np.dot(a3, model.W4) + model.b4
            a4 = model.softmax(z4)

            # 计算损失 (交叉熵损失)
            batch_loss = -np.sum(y_batch * np.log(a4 + 1e-10)) / x_batch.shape[0]
            total_loss += batch_loss

            # 反向传播 - 手动计算梯度 (4层网络)
            # 输出层梯度
            dz4 = a4 - y_batch  # softmax的梯度与交叉熵结合
            dW4 = np.dot(a3.T, dz4) / x_batch.shape[0]
            db4 = np.sum(dz4, axis=0, keepdims=True) / x_batch.shape[0]

            # 隐藏层3梯度
            da3 = np.dot(dz4, model.W4.T)
            dz3 = da3 * (z3 > 0)  # ReLU的梯度
            dW3 = np.dot(a2.T, dz3) / x_batch.shape[0]
            db3 = np.sum(dz3, axis=0, keepdims=True) / x_batch.shape[0]

            # 隐藏层2梯度
            da2 = np.dot(dz3, model.W3.T)
            dz2 = da2 * (z2 > 0)  # ReLU的梯度
            dW2 = np.dot(a1.T, dz2) / x_batch.shape[0]
            db2 = np.sum(dz2, axis=0, keepdims=True) / x_batch.shape[0]

            # 隐藏层1梯度
            da1 = np.dot(dz2, model.W2.T)
            dz1 = da1 * (z1 > 0)  # ReLU的梯度
            dW1 = np.dot(x_batch.T, dz1) / x_batch.shape[0]
            db1 = np.sum(dz1, axis=0, keepdims=True) / x_batch.shape[0]

            # 更新权重和偏置
            model.W4 -= learning_rate * dW4
            model.b4 -= learning_rate * db4
            model.W3 -= learning_rate * dW3
            model.b3 -= learning_rate * db3
            model.W2 -= learning_rate * dW2
            model.b2 -= learning_rate * db2
            model.W1 -= learning_rate * dW1
            model.b1 -= learning_rate * db1



        # 记录每个epoch的总损失
        epoch_loss = total_loss / len(train_loader)
        loss_history.append(epoch_loss)
        print(f"Epoch {epoch+1} 完成，平均损失: {epoch_loss:.4f}")

    # 绘制损失曲线
    plt.figure(figsize=(10, 5))
    plt.plot(range(1, epochs + 1), loss_history)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('训练损失曲线')
    plt.savefig('mlp_bp_loss.png')

# 测试函数
def test_model(model, test_loader):
    correct = 0
    total = 0

    for images, labels in test_loader:
        # 将PyTorch张量转换为NumPy数组并进行前向传播
        x_test = images.numpy().reshape(-1, 784)

        # 前向传播 - 4层网络
        z1 = np.dot(x_test, model.W1) + model.b1
        a1 = model.relu(z1)
        z2 = np.dot(a1, model.W2) + model.b2
        a2 = model.relu(z2)
        z3 = np.dot(a2, model.W3) + model.b3
        a3 = model.relu(z3)
        z4 = np.dot(a3, model.W4) + model.b4
        predictions = model.softmax(z4)

        # 获取预测的类别
        predicted_classes = np.argmax(predictions, axis=1)
        labels_np = labels.numpy()

        # 计算准确率
        correct += np.sum(predicted_classes == labels_np)
        total += labels.shape[0]

    accuracy = 100 * correct / total
    print(f"测试准确率: {accuracy:.2f}%")
    return accuracy

# 开始训练
train_model_with_bp(model, train_loader, epochs=epochs)

# 测试模型
test_model(model, test_loader)

# 将NumPy模型转换为PyTorch格式
def convert_to_pytorch_state_dict(model):
    """将NumPy模型权重转换为PyTorch state_dict格式"""
    import torch
    import torch.nn as nn

    # 创建与新PyTorch模型相同结构的参考模型
    class PyTorchMLP(nn.Module):
        def __init__(self):
            super(PyTorchMLP, self).__init__()
            self.net = nn.Sequential(
                nn.Linear(784, 256),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(128, 64),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(64, 10)
            )

        def forward(self, x):
            return self.net(x)

    # 创建一个空的PyTorch模型
    pytorch_model = PyTorchMLP()

    # 复制权重
    state_dict = pytorch_model.state_dict()

    # 复制NumPy权重到PyTorch state_dict (4层网络)
    with torch.no_grad():
        state_dict['net.0.weight'] = torch.FloatTensor(model.W1.T)
        state_dict['net.0.bias'] = torch.FloatTensor(model.b1.flatten())
        state_dict['net.3.weight'] = torch.FloatTensor(model.W2.T)
        state_dict['net.3.bias'] = torch.FloatTensor(model.b2.flatten())
        state_dict['net.6.weight'] = torch.FloatTensor(model.W3.T)
        state_dict['net.6.bias'] = torch.FloatTensor(model.b3.flatten())
        state_dict['net.9.weight'] = torch.FloatTensor(model.W4.T)
        state_dict['net.9.bias'] = torch.FloatTensor(model.b4.flatten())

    return state_dict

# 保存模型
os.makedirs("models", exist_ok=True)
state_dict = convert_to_pytorch_state_dict(model)
torch.save(state_dict, "models/mlp_bp.pth")
print("使用手写BP算法训练的MLP模型已成功保存到 models/mlp_bp.pth")