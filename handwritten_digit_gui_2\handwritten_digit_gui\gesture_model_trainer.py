import cv2
import mediapipe as mp
import numpy as np
import os
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, confusion_matrix
import pickle
import time

# 初始化MediaPipe Hands
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=True, max_num_hands=1, min_detection_confidence=0.5)
mp_drawing = mp.solutions.drawing_utils

# 数据集路径
DATASET_PATH = "dataset"
MODEL_FILENAME = "gesture_model.pkl"

def extract_features(image_path):
    """
    从单张图片中提取手部关键点特征。
    返回一个扁平化的关键点列表 (x, y, z * 21 个点)，或者在未检测到手时返回 None。
    """
    try:
        image = cv2.imread(image_path)
        if image is None:
            print(f"警告: 无法读取图片 {image_path}")
            return None

        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = hands.process(image_rgb)

        if results.multi_hand_landmarks:
            hand_landmarks = results.multi_hand_landmarks[0] # 只取第一只检测到的手
            
            # # 可视化 (调试用)
            # debug_image = image.copy()
            # mp_drawing.draw_landmarks(debug_image, hand_landmarks, mp_hands.HAND_CONNECTIONS)
            # cv2.imshow('Hand Landmarks', debug_image)
            # cv2.waitKey(1) # 等待1ms，允许窗口刷新

            landmarks = []
            for landmark in hand_landmarks.landmark:
                landmarks.extend([landmark.x, landmark.y, landmark.z])
            return landmarks
        else:
            # print(f"未在图片 {image_path} 中检测到手部。")
            return None
    except Exception as e:
        print(f"处理图片 {image_path} 时出错: {e}")
        return None

def load_data(dataset_path):
    """
    加载数据集，提取特征并打上标签。
    """
    features = []
    labels = []
    print("开始加载数据和提取特征...")
    
    # 获取所有子文件夹，即标签 (0-9)
    label_names = [d for d in os.listdir(dataset_path) if os.path.isdir(os.path.join(dataset_path, d))]
    label_names.sort() # 确保顺序

    for label_name in label_names:
        label_path = os.path.join(dataset_path, label_name)
        image_files = [f for f in os.listdir(label_path) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
     
        image_count = 0
        processed_count = 0
        for image_file in image_files:
            image_path = os.path.join(label_path, image_file)
            image_features = extract_features(image_path)
            if image_features:
                features.append(image_features)
                labels.append(int(label_name)) # 标签是文件夹名称 (数字)
                processed_count += 1
            image_count += 1
            if image_count % 50 == 0 : # 每处理50张图片打印一次进度
                 print(f"  已处理 {image_count}/{len(image_files)} 张图片 for label {label_name}...")


        print(f"标签 {label_name} 处理完成。成功提取特征的图片数量: {processed_count}/{len(image_files)}")

    if not features:
        print("错误：未能从数据集中提取任何特征。请检查数据集路径和图片内容。")
        return None, None
        
    # cv2.destroyAllWindows() # 关闭所有OpenCV窗口 (如果在extract_features中使用了imshow)
    return np.array(features), np.array(labels)

def train_model(features, labels):
    """
    训练手势识别模型。
    """
    if features is None or labels is None or len(features) == 0:
        print("错误：特征或标签为空，无法训练模型。")
        return None

    X_train, X_test, y_train, y_test = train_test_split(features, labels, test_size=0.2, random_state=42, stratify=labels)
    
    print(f"训练集大小: {X_train.shape[0]}, 测试集大小: {X_test.shape[0]}")

    if len(X_train) == 0:
        print("错误：训练集为空，无法训练。可能是由于数据量太少或特征提取失败导致。")
        return None

    print("开始训练SVM模型...")
  
    model = SVC(kernel='linear', probability=True, C=1.0) # 线性核通常训练更快，但可能效果不如RBF核
    
    start_time = time.time()
    model.fit(X_train, y_train)
    end_time = time.time()
    print(f"模型训练完成，耗时: {end_time - start_time:.2f} 秒")

    # 评估模型
    y_pred_train = model.predict(X_train)
    train_accuracy = accuracy_score(y_train, y_pred_train)
    print(f"训练集准确率: {train_accuracy * 100:.2f}%")

    if len(X_test) > 0:
        y_pred_test = model.predict(X_test)
        test_accuracy = accuracy_score(y_test, y_pred_test)
        print(f"测试集准确率: {test_accuracy * 100:.2f}%")
        print("测试集混淆矩阵:")
        print(confusion_matrix(y_test, y_pred_test))
    else:
        print("警告: 测试集为空，无法进行测试集评估。")

    return model

def save_model(model, filename=MODEL_FILENAME):
    """
    保存训练好的模型到文件。
    """
    if model is None:
        print("错误：模型为空，无法保存。")
        return
    try:
        with open(filename, 'wb') as f:
            pickle.dump(model, f)
        print(f"模型已成功保存到 {filename}")
    except Exception as e:
        print(f"保存模型失败: {e}")

if __name__ == "__main__":
    #  加载数据和提取特征
    features, labels = load_data(DATASET_PATH)

    if features is not None and labels is not None and len(features) > 0:
        # 训练模型
        trained_model = train_model(features, labels)

        #  保存模型
        if trained_model:
            save_model(trained_model)
    else:
        print("未能加载数据或提取特征，模型训练终止。")

    # 确保所有 MediaPipe 资源被释放 
    if 'hands' in globals() and hasattr(hands, 'close'):
        hands.close()